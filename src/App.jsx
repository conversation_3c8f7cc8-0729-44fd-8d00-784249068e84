import { useState } from 'react';
import { calculateInvestmentResults } from './util/investment';
import Result from './components/Result';
import InputGroup from './components/InputGroup';

const tempData = [
  {
    year: 1,
    interest: 900,
    valueEndOfYear: 5000,
    annualInvestment: 1000,
  },
  {
    year: 2,
    interest: 700,
    valueEndOfYear: 500,
    annualInvestment: 1500,
  },
  {
    year: 3,
    interest: 1000,
    valueEndOfYear: 3700,
    annualInvestment: 1000,
  },
  {
    year: 4,
    interest: 1100,
    valueEndOfYear: 4800,
    annualInvestment: 1250,
  },
];

function App() {
  const [annualData, setAnnualData] = useState(tempData);
  function handleInitInvest() {
    console.log('init invest');
  }

  function handleInputsComplete() {
    setAnnualData(
      calculateInvestmentResults({
        initialInvestment,
        annualInvestment,
        expectedReturn,
        duration,
      })
    );
  }

  return (
    <>
      <InputGroup onInitInvest={handleInitInvest} />
      <Result resultData={annualData} />
    </>
  );
}

export default App;
