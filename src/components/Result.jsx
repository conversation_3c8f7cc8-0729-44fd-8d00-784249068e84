import { formatter } from '../util/investment';

export default function Result({ resultData }) {
  return (
    <table id='result'>
      <thead>
        <tr>
          <th>Year</th>
          <th>Investment Value</th>
          <th>Interest (Year)</th>
          <th>Total Interest</th>
          <th>Invested Capital</th>
        </tr>
      </thead>
      <tbody>
        {resultData.map((item, index) => {
          return (
            <tr key={index}>
              <td>{item.year}</td>
              <td>{formatter.format(item.valueEndOfYear)}</td>
              <td>{formatter.format(item.annualInvestment)}</td>
              <td>{formatter.format(item.interest)}</td>
              <td>{formatter.format(item.annualInvestment)}</td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
}
