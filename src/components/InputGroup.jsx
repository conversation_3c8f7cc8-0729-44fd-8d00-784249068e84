import { useState } from 'react';

export default function InputGroup(
  onInitInvest,
  onAnnualInvest,
  onExpectedReturn,
  onDuration
) {
  return (
    <div id='user-input'>
      <div className='input-group'>
        <Input title='Initial Investment' onInputChange={onInitInvest} />
        <Input title='Annual Investment' onInputChange={onAnnualInvest} />
      </div>
      <br />
      <div className='input-group'>
        <Input title='Expected Return' onInputChange={onExpectedReturn} />
        <Input title='Duration' onInputChange={onDuration} />
      </div>
    </div>
  );
}

export function Input({ title, onInputChange }) {
  const [value, setValue] = useState(0);

  function handleOnChange(e) {
    setValue(e.target.value);
    onInputChange;
  }

  return (
    <div>
      <label>{title}</label>
      <input type='number' value={value} onChange={handleOnChange} />
    </div>
  );
}
